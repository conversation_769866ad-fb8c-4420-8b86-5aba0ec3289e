import React, { useState } from "react";
import { UserLayout } from "@/components/UserLayout";
import {
  FaSearch,
  FaFilter,
  FaChevronDown,
  FaAngleDown,
  FaShieldAlt,
  FaUniversity,
  FaUserShield,
  FaCreditCard,
  FaLock,
  FaCalendarAlt,
  FaGlobe,
  FaInfoCircle,
} from "react-icons/fa";
import { BsBank } from "react-icons/bs";

const AffiliateOffersPage: React.FC = () => {
  const [activeCategory, setActiveCategory] =
    useState<string>("All Categories");
  const [sortOption, setSortOption] = useState<string>("Recommended");

  // URL mapping for partner websites
  const partnerUrls: { [key: string]: string } = {
    "Aura Identity Protection": "https://www.aura.com/",
    "Aura": "https://www.aura.com/",
    "Brex Business Banking": "https://www.brex.com/",
    "Brex": "https://www.brex.com/",
    "Identity IQ": "https://www.identityiq.com",
    "CardMatch": "https://www.creditcards.com/",
    "Novo": "https://www.novo.co/",
    "Xero": "https://www.xero.com/",
    "IBC Global": "https://www.ibc.com/"
  };

  // Function to handle partner button clicks
  const handlePartnerClick = (partnerName: string) => {
    const url = partnerUrls[partnerName];
    if (url) {
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  };

  const categories = [
    { id: "all", name: "All Categories" },
    { id: "identity-theft", name: "Identity Theft" },
    { id: "credit-services", name: "Credit Services" },
    { id: "banking", name: "Banking" },
    { id: "bookkeeping", name: "Bookkeeping" },
    { id: "credit-cards", name: "Credit Cards" },
  ];

  const featuredPartners = [
    {
      id: 1,
      name: "Aura Identity Protection",
      icon: <FaShieldAlt />,
      description:
        "All-in-one digital safety. Fraud protection, credit monitoring, identity theft coverage for you and your family.",
      tags: ["Featured Partner", "Editor's Choice"],
      categories: ["Identity Theft", "Credit Monitoring"],
      backgroundColor: "bg-[#16c66c]",
    },
    {
      id: 2,
      name: "Brex Business Banking",
      icon: <FaUniversity />,
      description:
        "Modern financial stack for businesses. Fee-free, high-yield accounts and integrations with your accounting software.",
      tags: ["Featured Partner", "Most Recommended"],
      categories: ["Banking", "Financial Integration"],
      backgroundColor: "bg-purple-600",
    },
  ];

  const allPartners = [
    {
      id: 3,
      name: "Aura",
      icon: <FaUserShield />,
      category: "Identity Theft Protection",
      description:
        "All-in-one protection with credit monitoring, identity theft, insurance, and antivirus software.",
      tags: ["Top Rated"],
      categories: ["Identity Theft", "Credit Monitoring"],
      backgroundColor: "bg-blue-500",
    },
    {
      id: 4,
      name: "Identity IQ",
      icon: <FaLock />,
      category: "Identity Theft Monitoring",
      description:
        "Daily credit report monitoring from all three bureaus with identity theft insurance coverage.",
      tags: ["Editor's Choice"],
      categories: ["Credit Services", "Identity Theft"],
      backgroundColor: "bg-green-500",
    },
    {
      id: 5,
      name: "CardMatch",
      icon: <FaCreditCard />,
      category: "Credit Card Comparison",
      description:
        "Compare the best credit card offers and find the perfect match for your spending habits and credit score.",
      tags: ["Popular"],
      categories: ["Credit Cards", "Rewards Maximizer"],
      backgroundColor: "bg-purple-500",
    },
    {
      id: 6,
      name: "Brex",
      icon: <BsBank />,
      category: "Business Banking & Expense",
      description:
        "Modern financial stack for businesses with no fees, expense management, and rewards.",
      tags: ["Top Rated"],
      categories: ["Banking", "5% Back"],
      backgroundColor: "bg-blue-500",
    },
    {
      id: 7,
      name: "Novo",
      icon: <FaUniversity />,
      category: "Small Business Banking",
      description:
        "Powerfully simple business banking with no fees and integration with your favorite tools.",
      tags: ["Popular"],
      categories: ["Banking", "Integration"],
      backgroundColor: "bg-blue-500",
    },
    {
      id: 8,
      name: "Xero",
      icon: <FaCalendarAlt />,
      category: "Automated Bookkeeping",
      description:
        "AI-powered bookkeeping that automatically imports and categorizes transactions from financial sources.",
      tags: ["Bookkeeping"],
      categories: ["Bookkeeping", "AI-Powered"],
      backgroundColor: "bg-green-500",
    },
    {
      id: 9,
      name: "IBC Global",
      icon: <FaGlobe />,
      category: "International Business Solutions",
      description:
        "Global banking solutions for international businesses with multi-currency accounts and favorable transfer rates.",
      tags: ["Financial"],
      categories: ["International Banking", "Multi-Currency"],
      backgroundColor: "bg-yellow-500",
    },
  ];

  const whyPartnerReasons = [
    {
      id: 1,
      title: "Verified Quality",
      description:
        "We thoroughly vet all our partners to ensure they meet our high standards for service quality.",
      icon: <FaShieldAlt className="text-blue-500" />,
    },
    {
      id: 2,
      title: "Complementary Services",
      description:
        "Our partners offer services that complement your financial management journey.",
      icon: <FaInfoCircle className="text-blue-500" />,
    },
    {
      id: 3,
      title: "Exclusive Offers",
      description:
        "We negotiate special rates and discounts exclusively for our users.",
      icon: <FaCreditCard className="text-blue-500" />,
    },
  ];

  return (
    <UserLayout>
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-xl font-medium mb-2">Affiliate Offers</h1>
          <p className="text-gray-600 text-sm">
            Discover exclusive offers from our trusted partners in adjacent
            industries to help protect your identity, manage finances, and more.
          </p>
        </div>

        {/* Categories */}
        <div className="flex flex-wrap gap-2 mb-6">
          {categories.map((category) => (
            <button
              key={category.id}
              className={`px-4 py-2 text-sm rounded-md ${
                activeCategory === category.name
                  ? "bg-[#16c66c] text-white"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
              onClick={() => setActiveCategory(category.name)}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Featured Partners */}
        <div className="mb-8">
          <h2 className="font-medium text-gray-800 mb-4 flex items-center">
            <span className="w-4 h-4 rounded-full bg-yellow-400 mr-2"></span>
            Featured Partners
          </h2>
          <div className="grid md:grid-cols-2 gap-4">
            {featuredPartners.map((partner) => (
              <div
                key={partner.id}
                className={`${partner.backgroundColor} rounded-lg overflow-hidden text-white`}
              >
                <div className="p-5">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-2xl">
                      {partner.icon}
                    </div>
                    <div className="ml-4">
                      <h3 className="font-medium text-lg">{partner.name}</h3>
                      <div className="flex mt-1 gap-2">
                        {partner.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="text-xs px-2 py-0.5 bg-white bg-opacity-20 rounded-md"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-white text-opacity-90 mb-4">
                    {partner.description}
                  </p>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {partner.categories.map((cat, index) => (
                      <span
                        key={index}
                        className="text-xs px-2 py-0.5 bg-white bg-opacity-10 rounded-md"
                      >
                        {cat}
                      </span>
                    ))}
                  </div>
                  <button
                    className="bg-white text-blue-600 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50"
                    onClick={() => handlePartnerClick(partner.name)}
                  >
                    Visit Partner
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="font-medium text-gray-800 flex items-center">
              <span className="w-4 h-4 rounded-full bg-blue-500 mr-2"></span>
              All Partner Offers
            </h2>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Sort by:</span>
              <div className="relative">
                <select
                  className="appearance-none bg-white border border-gray-300 rounded-md px-3 py-1.5 pr-8 text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  value={sortOption}
                  onChange={(e) => setSortOption(e.target.value)}
                >
                  <option value="Recommended">Recommended</option>
                  <option value="Newest">Newest</option>
                  <option value="Highest Rated">Highest Rated</option>
                  <option value="Most Popular">Most Popular</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <FaChevronDown className="h-3 w-3" />
                </div>
              </div>
              <button className="p-2 text-gray-500 hover:bg-gray-100 rounded-md">
                <FaFilter size={16} />
              </button>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-4 mb-4">
            {allPartners.slice(0, 3).map((partner) => (
              <div
                key={partner.id}
                className="border border-gray-200 rounded-lg overflow-hidden"
              >
                <div className="p-4">
                  <div className="flex items-center mb-3">
                    <div
                      className={`w-8 h-8 ${partner.backgroundColor} text-white rounded-lg flex items-center justify-center text-sm`}
                    >
                      {partner.icon}
                    </div>
                    <div className="ml-3">
                      <h3 className="font-medium">{partner.name}</h3>
                      <span
                        className={`text-xs px-2 py-0.5 rounded-full ${
                          partner.tags[0] === "Top Rated"
                            ? "bg-yellow-100 text-yellow-800"
                            : partner.tags[0] === "Editor's Choice"
                              ? "bg-green-100 text-green-800"
                              : "bg-purple-100 text-purple-800"
                        }`}
                      >
                        {partner.tags[0]}
                      </span>
                    </div>
                  </div>
                  <div className="mb-3">
                    <div className="text-gray-700 font-medium text-sm mb-1">
                      {partner.category}
                    </div>
                    <p className="text-gray-600 text-xs leading-relaxed">
                      {partner.description}
                    </p>
                  </div>
                  <div className="flex flex-wrap gap-1 mb-3">
                    {partner.categories.map((cat, index) => (
                      <span
                        key={index}
                        className="text-xs px-2 py-0.5 rounded-md bg-gray-100 text-gray-600"
                      >
                        {cat}
                      </span>
                    ))}
                  </div>
                  <button
                    className="w-full py-2 text-center text-blue-600 bg-white border border-blue-600 rounded-md text-sm font-medium hover:bg-blue-50"
                    onClick={() => handlePartnerClick(partner.name)}
                  >
                    Visit Partner
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className="grid md:grid-cols-3 gap-4 mb-4">
            {allPartners.slice(3, 6).map((partner) => (
              <div
                key={partner.id}
                className="border border-gray-200 rounded-lg overflow-hidden"
              >
                <div className="p-4">
                  <div className="flex items-center mb-3">
                    <div
                      className={`w-8 h-8 ${partner.backgroundColor} text-white rounded-lg flex items-center justify-center text-sm`}
                    >
                      {partner.icon}
                    </div>
                    <div className="ml-3">
                      <h3 className="font-medium">{partner.name}</h3>
                      <span
                        className={`text-xs px-2 py-0.5 rounded-full ${
                          partner.tags[0] === "Top Rated"
                            ? "bg-yellow-100 text-yellow-800"
                            : partner.tags[0] === "Editor's Choice"
                              ? "bg-green-100 text-green-800"
                              : "bg-purple-100 text-purple-800"
                        }`}
                      >
                        {partner.tags[0]}
                      </span>
                    </div>
                  </div>
                  <div className="mb-3">
                    <div className="text-gray-700 font-medium text-sm mb-1">
                      {partner.category}
                    </div>
                    <p className="text-gray-600 text-xs leading-relaxed">
                      {partner.description}
                    </p>
                  </div>
                  <div className="flex flex-wrap gap-1 mb-3">
                    {partner.categories.map((cat, index) => (
                      <span
                        key={index}
                        className="text-xs px-2 py-0.5 rounded-md bg-gray-100 text-gray-600"
                      >
                        {cat}
                      </span>
                    ))}
                  </div>
                  <button
                    className="w-full py-2 text-center text-blue-600 bg-white border border-blue-600 rounded-md text-sm font-medium hover:bg-blue-50"
                    onClick={() => handlePartnerClick(partner.name)}
                  >
                    Visit Partner
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className="grid md:grid-cols-3 gap-4 mb-6">
            {allPartners.slice(6).map((partner) => (
              <div
                key={partner.id}
                className="border border-gray-200 rounded-lg overflow-hidden"
              >
                <div className="p-4">
                  <div className="flex items-center mb-3">
                    <div
                      className={`w-8 h-8 ${partner.backgroundColor} text-white rounded-lg flex items-center justify-center text-sm`}
                    >
                      {partner.icon}
                    </div>
                    <div className="ml-3">
                      <h3 className="font-medium">{partner.name}</h3>
                      <span
                        className={`text-xs px-2 py-0.5 rounded-full ${
                          partner.tags[0] === "Top Rated"
                            ? "bg-yellow-100 text-yellow-800"
                            : partner.tags[0] === "Editor's Choice"
                              ? "bg-green-100 text-green-800"
                              : partner.tags[0] === "Bookkeeping"
                                ? "bg-green-100 text-green-800"
                                : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {partner.tags[0]}
                      </span>
                    </div>
                  </div>
                  <div className="mb-3">
                    <div className="text-gray-700 font-medium text-sm mb-1">
                      {partner.category}
                    </div>
                    <p className="text-gray-600 text-xs leading-relaxed">
                      {partner.description}
                    </p>
                  </div>
                  <div className="flex flex-wrap gap-1 mb-3">
                    {partner.categories.map((cat, index) => (
                      <span
                        key={index}
                        className="text-xs px-2 py-0.5 rounded-md bg-gray-100 text-gray-600"
                      >
                        {cat}
                      </span>
                    ))}
                  </div>
                  <button
                    className="w-full py-2 text-center text-blue-600 bg-white border border-blue-600 rounded-md text-sm font-medium hover:bg-blue-50"
                    onClick={() => handlePartnerClick(partner.name)}
                  >
                    Visit Partner
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className="flex justify-center">
            <button className="text-blue-600 border border-blue-200 rounded-md px-4 py-2 flex items-center text-sm hover:bg-blue-50">
              View More Partners <FaAngleDown className="ml-1" />
            </button>
          </div>
        </div>

        {/* Why We Partner Section */}
        <div className="mb-8 bg-gray-50 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mr-3">
              <FaInfoCircle />
            </div>
            <h2 className="text-lg font-medium text-gray-800">
              Why We Partner With These Companies
            </h2>
          </div>
          <p className="text-gray-600 text-sm mb-6">
            We carefully select our partners to ensure they provide high-quality
            services that complement your financial journey. Each partner has
            been vetted for reliability, value, and customer satisfaction.
          </p>
          <div className="grid md:grid-cols-3 gap-6">
            {whyPartnerReasons.map((reason) => (
              <div
                key={reason.id}
                className="bg-white p-4 rounded-md shadow-sm border border-gray-100"
              >
                <div className="flex items-center mb-3">
                  <div className="w-7 h-7 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                    {reason.icon}
                  </div>
                  <h3 className="font-medium text-gray-800">{reason.title}</h3>
                </div>
                <p className="text-sm text-gray-600">{reason.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Affiliate Disclosure */}
        <div className="bg-gray-100 p-3 rounded-md text-xs text-gray-500 mb-4">
          <div className="font-medium mb-1">Affiliate Disclosure</div>
          <p>
            Stackeasy may receive compensation from our partners when you click
            on certain links. This compensation may impact which services we
            feature on our site. However, our recommendations are based on our
            independent research and thorough evaluations.
          </p>
        </div>
      </div>
    </UserLayout>
  );
};

export default AffiliateOffersPage;
